# AMC 12A 2002 Problem 13 - Proof Tree

## Problem Statement
Find the sum a + b of the two distinct positive numbers a, b for which each satisfies |x – 1/x| = 1.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that the sum of two distinct positive numbers satisfying |x - 1/x| = 1 equals √5
**Strategy**: Break down into equation analysis, quadratic solving, and sum calculation

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Convert absolute value equation |x - 1/x| = 1 to two cases
2. Solve x - 1/x = 1 to get x₁ = (1 + √5)/2
3. Solve x - 1/x = -1 to get x₂ = (-1 + √5)/2
4. Verify both solutions are positive and distinct
5. Calculate sum: x₁ + x₂ = √5

### SUBGOAL_001 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Convert |x - 1/x| = 1 to two separate equations
**Strategy**: Use absolute value definition: |A| = 1 means A = 1 or A = -1
**Mathlib Reference**: abs_of_nonneg, abs_of_neg
**Proof Completion**: Used case analysis on sign of (x - 1/x) with abs_of_nonneg and abs_of_neg

### SUBGOAL_002 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Solve x - 1/x = 1 for positive x to get x₁ = (1 + √5)/2
**Strategy**: Multiply by x to get x² - x - 1 = 0, apply quadratic formula
**Mathlib Reference**: div_pos, Real.sqrt_pos, field_simp, ring_nf, Real.sq_sqrt
**Proof Completion**: Used field_simp and ring_nf to verify the golden ratio satisfies x - 1/x = 1

### SUBGOAL_003 [PROMISING]
**Parent Node**: STRATEGY_001
**Goal**: Solve x - 1/x = -1 for positive x to get x₂ = (-1 + √5)/2
**Strategy**: Multiply by x to get x² + x - 1 = 0, apply quadratic formula
**Mathlib Reference**: div_pos, Real.sqrt_pos, field_simp, ring_nf, Real.sq_sqrt
**Concrete Tactics**:
```lean
use (-1 + Real.sqrt 5) / 2
constructor
· -- prove positivity
  apply div_pos
  · linarith [Real.sqrt_pos.mpr (by norm_num : (0 : ℝ) < 5)]
  · norm_num
constructor
· -- prove equation
  field_simp
  ring_nf
  rw [Real.sq_sqrt (by norm_num : (0 : ℝ) ≤ 5)]
  ring
· -- prove equality
  rfl
```

### SUBGOAL_004 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Verify both solutions are positive and distinct
**Strategy**: Check discriminants and sign analysis using √5 > 2
**Mathlib Reference**: Real.sqrt_pos, ne_of_gt

### SUBGOAL_005 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Calculate sum: (1 + √5)/2 + (-1 + √5)/2 = √5
**Strategy**: Algebraic simplification: (1 + √5 - 1 + √5)/2 = 2√5/2 = √5
**Mathlib Reference**: add_div, Real.sqrt_mul

## Current Status
- Phase 1: Proof tree initialization complete
- Next: Generate code framework with sorry placeholders
