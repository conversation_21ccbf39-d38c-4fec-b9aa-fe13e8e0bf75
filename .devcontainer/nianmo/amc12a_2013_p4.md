# AMC 12A 2013 Problem 4 - Proof Tree

## Problem Statement
Evaluate $\frac{2^{2014}+2^{2012}}{2^{2014}-2^{2012}}$

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that $\frac{2^{2014}+2^{2012}}{2^{2014}-2^{2012}} = \frac{5}{3}$
**Parent Node**: None
**Strategy**: Factor out common power and simplify

### STRATEGY_001 [STRATEGY] 
**Goal**: Use factorization approach to simplify the expression
**Parent Node**: ROOT_001
**Detailed Plan**: 
1. Factor out $2^{2012}$ from both numerator and denominator
2. Simplify the resulting expression with smaller exponents
3. Compute the final rational number

### SUBGOAL_001 [TO_EXPLORE]
**Goal**: Factor out $2^{2012}$ from numerator and denominator
**Parent Node**: STRATEGY_001
**Strategy**: Use the identity $2^{2014} = 2^{2012} \cdot 2^2$ to rewrite both terms
**Detailed Plan**: 
- Rewrite $2^{2014} + 2^{2012} = 2^{2012}(2^2 + 1) = 2^{2012}(4 + 1) = 2^{2012} \cdot 5$
- Rewrite $2^{2014} - 2^{2012} = 2^{2012}(2^2 - 1) = 2^{2012}(4 - 1) = 2^{2012} \cdot 3$

### SUBGOAL_002 [TO_EXPLORE]
**Goal**: Simplify the fraction after factorization
**Parent Node**: STRATEGY_001
**Strategy**: Cancel common factors and compute the result
**Detailed Plan**:
- After factorization: $\frac{2^{2012} \cdot 5}{2^{2012} \cdot 3}$
- Cancel $2^{2012}$ from numerator and denominator
- Result: $\frac{5}{3}$

### SUBGOAL_003 [TO_EXPLORE]
**Goal**: Formalize the proof in Lean 4 with proper theorem statement
**Parent Node**: STRATEGY_001
**Strategy**: Create a theorem with explicit computation steps
**Detailed Plan**:
- Define theorem with the exact expression
- Use Lean 4 arithmetic simplification tactics
- Verify the computation equals 5/3
